/**
 * 闲鱼商品自动发布脚本
 * 基于 Playwright 自动化浏览器操作
 * 
 * 功能：
 * 1. 自动打开闲鱼商品发布页面
 * 2. 填写商品分类和属性
 * 3. 上传商品图片
 * 4. 填写商品信息
 * 5. 设置价格和库存
 * 6. 发布商品
 */

const { chromium } = require('playwright');
const path = require('path');

class GoofishProductPublisher {
    constructor() {
        this.browser = null;
        this.context = null;
        this.page = null;
    }

    /**
     * 初始化浏览器
     */
    async init(options = {}) {
        const userDataDir = "C:/Users/<USER>/AppData/Local/ms-playwright/mcp-chrome/"

        const usePersistentContext = options.usePersistentContext !== false; // 默认使用持久化上下文

        if (usePersistentContext) {
            // 使用持久化上下文，支持用户数据目录
            const contextOptions = {
                headless: false,
                slowMo: 1000,
                viewport: { width: 1920, height: 1080 },
                // 反检测设置
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled'
                ],
                ...options.contextOptions
            };

            this.context = await chromium.launchPersistentContext(userDataDir, contextOptions);
            this.browser = this.context.browser();
            this.page = this.context.pages()[0] || await this.context.newPage();

            // 设置反检测脚本
            await this.setupAntiDetection();

            console.log(`🗂️ 使用持久化用户数据目录: ${userDataDir}`);
        } else {
            // 使用普通浏览器启动（不保存用户数据）
            const launchOptions = {
                headless: false,
                slowMo: 1000,
                // 反检测设置
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-blink-features=AutomationControlled'
                ],
                ...options.launchOptions
            };

            this.browser = await chromium.launch(launchOptions);
            this.context = await this.browser.newContext({
                viewport: { width: 1920, height: 1080 },
                userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                ...options.contextOptions
            });
            this.page = await this.context.newPage();

            // 设置反检测脚本
            await this.setupAntiDetection();

            console.log('🗂️ 使用临时浏览器会话（不保存用户数据）');
        }
    }

    /**
     * 设置反检测脚本
     */
    async setupAntiDetection() {
        // 移除webdriver属性
        await this.page.addInitScript(() => {
            // 删除webdriver属性
            delete navigator.__proto__.webdriver;

            // 重写navigator.webdriver
            Object.defineProperty(navigator, 'webdriver', {
                get: () => false,
            });

            // 重写chrome属性
            Object.defineProperty(navigator, 'chrome', {
                get: () => ({
                    runtime: {},
                    // 其他chrome属性...
                }),
            });

            // 重写permissions属性
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            // 重写plugins属性
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });

            // 重写languages属性
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
        });
    }

    /**
     * 打开商品发布页面
     */
    async openProductAddPage() {
        console.log('🌐 正在打开商品发布页面...');
        await this.page.goto('https://www.goofish.pro/sale/product/add');
        await this.page.waitForLoadState('networkidle');
        console.log('✅ 页面加载完成');
    }

    /**
     * 选择商品分类
     */
    async selectCategory() {
        console.log('📂 正在选择商品分类...');
        
        // 等待商品类型选项加载
        await this.page.waitForSelector('input[type="radio"]', { timeout: 10000 });
        
        // 选择"普通商品"选项
        await this.page.getByRole('radio', { name: /普通商品/ }).click();

        // 2. 点击分类输入框
        await this.page.getByRole('textbox', { name: '请输入关键词' }).click();

        // 3. 选择第1层：3C数码
        await this.page.getByText('3C数码').click();

        // 4. 选择第2层：笔记本电脑 (first())
        await this.page.getByText('笔记本电脑').first().click();

        // 5. 选择第3层：笔记本电脑 (nth(1))
        await this.page.getByText('笔记本电脑').nth(1).click();

        // 6. 选择第4层：笔记本电脑 (nth(2)) - 这里才是你原代码应该工作的地方
        await this.page.getByText('笔记本电脑').nth(2).click();
        
        console.log('✅ 商品分类选择完成：3C数码/笔记本电脑');
    }

    /**
     * 填写商品属性
     */
    async fillProductAttributes() {
        console.log('🏷️ 正在填写商品属性...');

        // 选择品牌：Apple/苹果
        await this.page.click('text=Apple/苹果');
        await this.page.waitForTimeout(1000);

        // 选择型号：MacBook Pro 2023 14寸
        await this.page.click('text=MacBook Pro 2023 14寸');
        await this.page.waitForTimeout(1000);

        // 选择成色：几乎全新
        await this.page.click('text=几乎全新');
        await this.page.waitForTimeout(1000);

        // 选择购买渠道：中国大陆
        await this.page.click('text=中国大陆');
        await this.page.waitForTimeout(1000);

        // 选择内存容量：16GB
        await this.page.click('text=16GB');
        await this.page.waitForTimeout(1000);

        console.log('✅ 商品属性填写完成');
    }

    /**
     * 上传商品图片
     */
    async uploadProductImage(imagePath) {
        console.log('🖼️ 正在上传商品图片...');
        
        // 点击上传图片按钮
        const uploadButton = await this.page.$('text=上传图片');
        if (uploadButton) {
            // 设置文件选择器
            const [fileChooser] = await Promise.all([
                this.page.waitForEvent('filechooser'),
                uploadButton.click()
            ]);
            
            // 上传文件
            await fileChooser.setFiles(imagePath);
            await this.page.waitForTimeout(3000); // 等待图片上传完成
            
            console.log('✅ 图片上传完成');
        }
    }

    /**
     * 填写商品信息
     */
    async fillProductInfo(productData) {
        console.log('📝 正在填写商品信息...');

        // 填写商品标题
        const titleInput = await this.page.$('input[placeholder*="商品标题"]');
        if (titleInput) {
            await titleInput.fill(productData.title);
        }

        // 填写商品描述
        const descInput = await this.page.$('textarea[placeholder*="商品描述"]');
        if (descInput) {
            await descInput.fill(productData.description);
        }

        console.log('✅ 商品信息填写完成');
    }

    /**
     * 设置价格和库存
     */
    async setPriceAndStock(price, stock = 1) {
        console.log('💰 正在设置价格和库存...');

        // 设置售价
        const priceInput = await this.page.$('input[placeholder*="0.00"]');
        if (priceInput) {
            await priceInput.fill(price.toString());
        }

        // 设置库存（默认为1）
        const stockInputs = await this.page.$$('input[type="number"]');
        for (let input of stockInputs) {
            const value = await input.inputValue();
            if (value === '1' || value === '') {
                await input.fill(stock.toString());
                break;
            }
        }

        console.log(`✅ 价格设置为：¥${price}，库存：${stock}件`);
    }

    /**
     * 选择发布的闲鱼店铺
     */
    async selectXianyuShop() {
        console.log('🏪 正在选择发布的闲鱼店铺...');

        try {
            // 等待店铺选择区域加载
            await this.page.waitForSelector('text=发布的闲鱼店铺', { timeout: 5000 });

            // 方法1：通过具体的店铺列表选择器
            const shopList = await this.page.$('list');
            if (shopList) {
                const shopItems = await shopList.$$('listitem');

                for (let i = 0; i < shopItems.length; i++) {
                    const item = shopItems[i];
                    const text = await item.textContent();

                    // 跳过"创建闲鱼店铺"选项，选择实际的店铺
                    if (text && !text.includes('创建闲鱼店铺') && text.trim().length > 5) {
                        await item.click();
                        console.log(`✅ 已选择店铺：${text.trim().substring(0, 50)}...`);
                        await this.page.waitForTimeout(1000);
                        return true;
                    }
                }
            }

            // 方法2：通过图片元素查找店铺（店铺通常有头像）
            const shopWithImage = await this.page.$('listitem img');
            if (shopWithImage) {
                const parentItem = await shopWithImage.evaluateHandle(el => el.closest('listitem'));
                if (parentItem) {
                    await parentItem.click();
                    const shopText = await parentItem.textContent();
                    console.log(`✅ 已选择有头像的店铺：${shopText?.trim().substring(0, 30)}...`);
                    await this.page.waitForTimeout(1000);
                    return true;
                }
            }

            // 方法3：通过店铺名称文本直接查找
            const shopNameElements = await this.page.$$('text=/^(?!.*创建闲鱼店铺).+工作室|.+店铺|.+商城/');
            if (shopNameElements.length > 0) {
                await shopNameElements[0].click();
                const shopText = await shopNameElements[0].textContent();
                console.log(`✅ 已选择店铺：${shopText?.trim()}`);
                await this.page.waitForTimeout(1000);
                return true;
            }

            console.log('⚠️ 未找到可用的闲鱼店铺，可能需要先创建店铺');

            // 尝试截图以便调试
            await this.page.screenshot({ path: 'shop-selection-debug.png' });
            console.log('📸 已保存店铺选择区域截图：shop-selection-debug.png');

            return false;

        } catch (error) {
            console.error('❌ 选择闲鱼店铺时出错：', error);
            return false;
        }
    }

    /**
     * 发布商品
     */
    async publishProduct() {
        console.log('🚀 正在发布商品...');

        try {
            // 查找并点击确定按钮
            const confirmButton = await this.page.$('button:has-text("确定")');
            if (!confirmButton) {
                console.log('❌ 未找到确定按钮');
                return false;
            }

            await confirmButton.click();
            console.log('✅ 已点击确定按钮');

            // 等待发布完成或出现错误提示
            await this.page.waitForTimeout(5000);

            // 检查是否发布成功
            const successMessage = await this.page.$('text=发布成功');
            if (successMessage) {
                console.log('🎉 商品发布成功！');
                return true;
            }

            // 检查是否有错误提示
            const errorSelectors = [
                'text*=失败',
                'text*=错误',
                'text*=请选择',
                'text*=必填',
                'text*=不能为空'
            ];

            for (let selector of errorSelectors) {
                const errorElements = await this.page.$$(selector);
                if (errorElements.length > 0) {
                    for (let element of errorElements) {
                        const errorText = await element.textContent();
                        if (errorText && errorText.trim()) {
                            console.log(`❌ 发布失败：${errorText.trim()}`);
                        }
                    }
                    return false;
                }
            }

            // 如果没有明确的成功或失败消息，检查页面URL变化
            const currentUrl = this.page.url();
            if (currentUrl.includes('success') || currentUrl.includes('complete')) {
                console.log('🎉 根据URL变化判断发布成功！');
                return true;
            }

            console.log('⚠️ 发布状态不明确，请手动检查');
            return false;

        } catch (error) {
            console.error('❌ 发布商品时出错：', error);
            return false;
        }
    }

    /**
     * 主要发布流程
     */
    async publishProductFlow(productData, imagePath, options = {}) {
        try {
            await this.init(options);
            await this.openProductAddPage();

            // 1. 选择商品分类
            await this.selectCategory();

            // 2. 填写商品属性
            await this.fillProductAttributes();

            // 3. 上传商品图片
            await this.uploadProductImage(imagePath);

            // 4. 填写商品信息
            await this.fillProductInfo(productData);

            // 5. 设置价格和库存
            await this.setPriceAndStock(productData.price, productData.stock);

            // 6. 选择发布的闲鱼店铺（在发布前必须选择）
            const shopSelected = await this.selectXianyuShop();
            if (!shopSelected) {
                console.log('❌ 未能选择闲鱼店铺，发布流程终止');
                return false;
            }

            // 7. 发布商品
            const success = await this.publishProduct();

            if (success) {
                console.log('🎉 商品发布流程完成！');
                return true;
            } else {
                console.log('❌ 商品发布失败，请检查页面状态');

                // 保存失败时的页面截图
                await this.page.screenshot({ path: 'publish-failed.png' });
                console.log('📸 已保存失败截图：publish-failed.png');
                return false;
            }

        } catch (error) {
            console.error('❌ 发布过程中出现错误：', error);

            // 保存错误时的页面截图
            try {
                await this.page.screenshot({ path: 'error-screenshot.png' });
                console.log('📸 已保存错误截图：error-screenshot.png');
            } catch (screenshotError) {
                console.warn('⚠️ 无法保存错误截图');
            }

            return false;
        } finally {
            // 等待5秒后关闭浏览器（可选）
            await this.page.waitForTimeout(5000);
            // await this.close();
        }
    }

    /**
     * 调试方法：检查页面状态
     */
    async debugPageStatus() {
        console.log('🔍 正在检查页面状态...');

        try {
            // 检查当前URL
            console.log(`📍 当前URL: ${this.page.url()}`);

            // 检查是否有错误消息
            const errorElements = await this.page.$$('[class*="error"], [class*="warning"], .ant-message-error, .ant-notification-notice-error');
            if (errorElements.length > 0) {
                console.log('❌ 发现错误消息:');
                for (let element of errorElements) {
                    const text = await element.textContent();
                    if (text && text.trim()) {
                        console.log(`   - ${text.trim()}`);
                    }
                }
            }

            // 检查必填字段
            const requiredFields = await this.page.$$('input[required], select[required], textarea[required]');
            console.log(`📝 必填字段数量: ${requiredFields.length}`);

            // 检查店铺选择状态
            const shopSection = await this.page.$('text=发布的闲鱼店铺');
            if (shopSection) {
                const shopList = await this.page.$('list');
                if (shopList) {
                    const selectedShop = await shopList.$('[class*="selected"], [class*="active"]');
                    if (selectedShop) {
                        const shopText = await selectedShop.textContent();
                        console.log(`🏪 已选择店铺: ${shopText?.trim()}`);
                    } else {
                        console.log('⚠️ 未检测到已选择的店铺');
                    }
                }
            }

        } catch (error) {
            console.error('❌ 检查页面状态时出错：', error);
        }
    }

    /**
     * 关闭浏览器
     */
    async close() {
        try {
            if (this.context) {
                await this.context.close();
            } else if (this.browser) {
                await this.browser.close();
            }
        } catch (error) {
            console.warn('⚠️ 关闭浏览器时出现警告：', error.message);
        }
    }
}

// 使用示例
async function main() {
    const publisher = new GoofishProductPublisher();

    // 商品数据
    const productData = {
        title: '苹果MacBook Pro 2023 14寸 16GB 几乎全新',
        description: '苹果MacBook Pro 2023款 14寸笔记本电脑，配置16GB内存，几乎全新成色，中国大陆购买，无任何拆修记录。外观精美，性能强劲，适合办公、设计、编程等多种用途。原装配件齐全，支持包邮发货。',
        price: 12800,
        stock: 1
    };

    // 图片路径（请确保文件存在）
    const imagePath = path.join(__dirname, 'apple.jpg');

    // 浏览器选项 - 使用与MCP Playwright相同的用户数据目录
    const options = {
        usePersistentContext: true, // 使用持久化上下文
        userDataDir: undefined, // 使用默认MCP目录
        contextOptions: {
            headless: false,
            slowMo: 1000,
            viewport: { width: 1920, height: 1080 }
        }
    };

    // 执行发布流程
    await publisher.publishProductFlow(productData, imagePath, options);
}

// 运行脚本
if (require.main === module) {
    main().catch(console.error);
}

module.exports = GoofishProductPublisher;
