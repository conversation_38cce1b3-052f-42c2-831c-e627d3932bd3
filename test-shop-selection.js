/**
 * 测试闲鱼店铺选择功能
 */

const GoofishProductPublisher = require('./goofish-product-publisher.js');

async function testShopSelection() {
    const publisher = new GoofishProductPublisher();
    
    try {
        console.log('🧪 开始测试店铺选择功能...');
        
        // 初始化浏览器
        await publisher.init({
            usePersistentContext: true,
            contextOptions: {
                headless: false,
                slowMo: 1000
            }
        });
        
        // 打开商品发布页面
        await publisher.openProductAddPage();
        
        // 选择商品分类
        await publisher.selectCategory();
        
        // 等待页面加载完成
        await publisher.page.waitForTimeout(3000);
        
        // 测试店铺选择
        console.log('\n🏪 测试店铺选择功能...');
        const shopSelected = await publisher.selectXianyuShop();
        
        if (shopSelected) {
            console.log('✅ 店铺选择测试成功！');
        } else {
            console.log('❌ 店铺选择测试失败！');
        }
        
        // 调试页面状态
        await publisher.debugPageStatus();
        
        // 保持浏览器打开以便手动检查
        console.log('\n🔍 浏览器将保持打开状态，请手动检查店铺选择结果...');
        console.log('按 Ctrl+C 退出测试');
        
        // 等待用户手动检查
        await new Promise(resolve => {
            process.on('SIGINT', () => {
                console.log('\n👋 测试结束');
                resolve();
            });
        });
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误：', error);
    } finally {
        // await publisher.close();
    }
}

// 运行测试
if (require.main === module) {
    testShopSelection().catch(console.error);
}

module.exports = testShopSelection;
